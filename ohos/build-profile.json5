{
  "app": {
    "signingConfigs": [
      {
        "name": "default",
        "type": "HarmonyOS",
        "material": {
          "certpath": "/Users/<USER>/.ohos/config/default_ohos_Y7ZrGxPmoSNCgyutC9pkc63zjAiDQgd2rOO0022aNUU=.cer",
          "keyAlias": "debugKey",
          "keyPassword": "0000001B69193BF6DF17E96273AD2BB2651E82A84A7A4D482EBBADE97872528AFE76DC68DB4917555AFA75",
          "profile": "/Users/<USER>/.ohos/config/default_ohos_Y7ZrGxPmoSNCgyutC9pkc63zjAiDQgd2rOO0022aNUU=.p7b",
          "signAlg": "SHA256withECDSA",
          "storeFile": "/Users/<USER>/.ohos/config/default_ohos_Y7ZrGxPmoSNCgyutC9pkc63zjAiDQgd2rOO0022aNUU=.p12",
          "storePassword": "0000001B123DD6094BCAF786BD847033B0F3B68B6650E0EED868D011C3090D8949D1DFAF6788B2EADD548F"
        }
      }
    ],
    "products": [
      {
        "name": "default",
        "signingConfig": "default",
        "compatibleSdkVersion": "5.0.0(12)",
        "runtimeOS": "HarmonyOS",
      }
    ],
    "buildModeSet": [
      {
        "name": "debug"
      },
      {
        "name": "profile"
      },
      {
        "name": "release"
      }
    ]
  },
  "modules": [
    {
      "name": "entry",
      "srcPath": "./entry",
      "targets": [
        {
          "name": "default",
          "applyToProducts": [
            "default"
          ]
        }
      ]
    }
  ]
}